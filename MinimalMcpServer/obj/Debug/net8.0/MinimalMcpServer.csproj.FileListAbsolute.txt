/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/MinimalMcpServer
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/MinimalMcpServer.deps.json
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/MinimalMcpServer.runtimeconfig.json
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/MinimalMcpServer.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/MinimalMcpServer.pdb
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.AI.Abstractions.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Configuration.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Binder.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Json.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.DependencyInjection.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Diagnostics.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.FileProviders.Physical.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Hosting.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Logging.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Logging.Abstractions.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Logging.Configuration.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Logging.Console.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Logging.Debug.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Logging.EventLog.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Logging.EventSource.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Options.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/Microsoft.Extensions.Primitives.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/ModelContextProtocol.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/ModelContextProtocol.Core.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/System.Diagnostics.DiagnosticSource.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/System.Diagnostics.EventLog.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/System.IO.Pipelines.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/System.Net.ServerSentEvents.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/System.Text.Encodings.Web.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/System.Text.Json.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll
/Users/<USER>/mcp/MinimalMcpServer/bin/Debug/net8.0/runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll
/Users/<USER>/mcp/MinimalMcpServer/obj/Debug/net8.0/MinimalMcpServer.csproj.AssemblyReference.cache
/Users/<USER>/mcp/MinimalMcpServer/obj/Debug/net8.0/MinimalMcpServer.GeneratedMSBuildEditorConfig.editorconfig
/Users/<USER>/mcp/MinimalMcpServer/obj/Debug/net8.0/MinimalMcpServer.AssemblyInfoInputs.cache
/Users/<USER>/mcp/MinimalMcpServer/obj/Debug/net8.0/MinimalMcpServer.AssemblyInfo.cs
/Users/<USER>/mcp/MinimalMcpServer/obj/Debug/net8.0/MinimalMcpServer.csproj.CoreCompileInputs.cache
/Users/<USER>/mcp/MinimalMcpServer/obj/Debug/net8.0/MinimalM.EEE580C0.Up2Date
/Users/<USER>/mcp/MinimalMcpServer/obj/Debug/net8.0/MinimalMcpServer.dll
/Users/<USER>/mcp/MinimalMcpServer/obj/Debug/net8.0/refint/MinimalMcpServer.dll
/Users/<USER>/mcp/MinimalMcpServer/obj/Debug/net8.0/MinimalMcpServer.pdb
/Users/<USER>/mcp/MinimalMcpServer/obj/Debug/net8.0/MinimalMcpServer.genruntimeconfig.cache
/Users/<USER>/mcp/MinimalMcpServer/obj/Debug/net8.0/ref/MinimalMcpServer.dll
